const logger = require('./Logger');

const NodeApp = require('./NodeApp.js')
logger.info('Application startup: Node.js application process initiated.');
NodeApp.init()

// Example: Simulate an unhandled promise rejection after a delay
// This will be caught by the Logger's `handleRejections: true` transport and logged to file.
setTimeout(() => {
    Promise.reject(new Error('Simulated unhandled promise rejection in main.js after 10s'));
}, 10000);

// Example: Simulate an uncaught synchronous exception after a delay
// This will be caught by the Logger's `handleExceptions: true` transport and logged to file.
setTimeout(() => {
    logger.info('Simulating an uncaught synchronous error in main.js...');
    // This will cause an uncaught exception
    throw new Error('Simulated uncaught synchronous exception in main.js after 15s');
}, 15000);


module.exports = {}

