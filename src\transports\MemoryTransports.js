// /public/src/transports/MemoryTransport.js
const Transport = require('winston-transport');

class MemoryTransport extends Transport {
    constructor(options) {
        super(options);
        this.logs = []; // Stores log objects
        this.maxSize = options.maxSize || (5 * 1024 * 1024); // Default 5MB limit in bytes
        this.currentSize = 0; // Tracks current size of stored logs

        // Minimal overhead for this event
        this.on('logged', () => {});
    }

    log(info, callback) {
        // Asynchronously process the log to avoid blocking the main thread
        setImmediate(() => {
            this.emit('logged', info);
        });

        // Convert the log object to a string to calculate its memory footprint accurately
        // Handle potential circular references gracefully to prevent errors during stringification
        let logEntryString;
        try {
            logEntryString = JSON.stringify(info) + '\n';
        } catch (e) {
            // Fallback for circular structures or other stringification issues
            logEntryString = JSON.stringify({
                level: info.level,
                message: info.message,
                meta: '[Circular structure or other JSON error in log object]'
            }) + '\n';
            // Optionally log this specific error to console/persistent if it indicates a problem
            // console.warn('MemoryTransport: Failed to stringify log entry for size calculation.', e.message);
        }

        const entrySize = Buffer.byteLength(logEntryString, 'utf8');

        // Manage memory size: remove oldest entries if adding the new one would exceed maxSize
        // This 'while' loop ensures we stay within the memory limit
        while (this.currentSize + entrySize > this.maxSize && this.logs.length > 0) {
            const oldestLog = this.logs.shift(); // Remove the oldest log entry
            // Subtract its size from currentSize
            this.currentSize -= Buffer.byteLength(JSON.stringify(oldestLog) + '\n', 'utf8');
        }

        // Add the new log entry if it fits (or if it's not excessively large itself)
        if (entrySize <= this.maxSize) { // Ensure a single log isn't larger than max size
            this.logs.push(info);
            this.currentSize += entrySize;
        } else {
            // Log message dropped if it's too large to fit even as a single entry
            console.warn(`MemoryTransport: Dropped log entry due to excessive size (${(entrySize / (1024*1024)).toFixed(2)}MB > ${(this.maxSize / (1024*1024)).toFixed(2)}MB): ${info.message}`);
        }

        // Signal completion if a callback is provided
        if (callback) {
            callback();
        }
    }

    /**
     * Optional: Method to retrieve all current logs from memory.
     * Use sparingly in production to avoid copying large arrays.
     */
    getLogs() {
        return [...this.logs]; // Return a copy to prevent external modification
    }

    /**
     * Optional: Method to clear all logs from memory.
     */
    clearLogs() {
        this.logs = [];
        this.currentSize = 0;
        // console.log('MemoryTransport: Logs cleared.');
    }
}

module.exports = MemoryTransport;