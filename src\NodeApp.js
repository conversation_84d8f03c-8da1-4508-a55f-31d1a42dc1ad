//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// NodeApp.js
//
// The top level file to import into NodeRed
//=============================================================================================
console.log('========== NodeApp.js ==============')

const fs = require('fs')
const { clearObject, startAlignedInterval, arrayToObject } = require('./Util.js')

const { init: IoServersInit   } = require('./IoServersInit.js')
const { init: IoRegistersInit } = require('./IoRegistersInit.js')
const IoService        = require('./IoService.js')
const HttpServer       = require('./HttpServer.js')
const System           = require('./System.js')
const Datalog          = require('./Datalog.js')
const CurvesInit       = require('./CurvesInit.js')
const ProjectConstants = require('./ProjectConstants.js')
const Schedules        = require('./Schedules.js')
const DocsInit         = require('./DocsInit.js')
const DataPoster       = require('./data_poster/data_poster.js')

const { sseBroadcast } = require('./routes/sse.js')

const {
	ProjectDefinitions,

	IoServersArray,
	IoServersArrayDev,

	RegistersList,
	RegistersPersist,

	Dashboards,
	UnitsConversions,
	Trends,
	DatalogList,
	// CurvesFile,

	// This will be called every 1 second by the system
	Poll: NarrativePoll,
} = require('./Project.js')



const Global = require('./Global.js')
const {
	init: globalInit,
	registers,
	devicesList,
	ioServers,
	datalogSettings,
	projectSettings,
	timestamp,
	trends,
} = Global


let SystemTimerClear = null
const SystemTimerInterval = 1000
let systemTimerCallback = null
let systemTimerCounter = 0

const 	RegistersListClean = []


/**
 * Main Initialization for the Node application to be used inside the NodeRed application
 * @function init
 * @description Initializes the external NodeRed application component
 * @returns {void}
 */
function init()
{
	// if(SystemTimerClear) {
	// 	SystemTimerClear()
	// 	SystemTimerClear = null
	// }

	// Make sure the system directory exists for User login
	System.init()
// console.log('+++++>>> System.secrets.jwtSecret:', System.secrets.jwtSecret)
// process.exit(0)

	//-----------------------------------------------------------
	// Clear system timer
	//-----------------------------------------------------------
	SystemTimerClear?.()
	SystemTimerClear = null

	systemTimerCounter = 0
	HttpServer.close()

	//-----------------------------------------------------------
	// Load All Elements of `Global`
	//-----------------------------------------------------------
	globalInit()
	trends.length = 0
	trends.push(...Trends)
	ProjectConstants.init()
	Schedules.init()
	DocsInit.init()


	const myIoServersArray = process.env.PRAEVISTA_DEBUG ? IoServersArrayDev : IoServersArray
	const tmpIoServers = arrayToObject(myIoServersArray, 'name')
	clearObject(ioServers)
	Object.assign(ioServers, tmpIoServers)

	CurvesInit()

	//-----------------------------------------------------------
	// registers
	//-----------------------------------------------------------
	clearObject(registers)
	const { registersObject, registersArray } = IoRegistersInit(RegistersList, ioServers, projectSettings)
	Object.assign(registers, registersObject)
	RegistersListClean.length = 0
	RegistersListClean.push(...registersArray)
	devicesList.length = 0
	devicesList.push(...Object.keys(registers))

	//-----------------------------------------------------------
	// ioServers - Global version used in realtime
	// CHANGE: close any outstanding connections
	//-----------------------------------------------------------
	// To set process.env.PRAEVISTA_DEBUG, edit `/lib/systemd/system/nodered.service`
	// Add: Environment="PRAEVISTA_DEBUG=1"
	// IoServersInit is only called after the registers are initialized
	// const myIoServers = IoServersInit(ioServers, registersArray)


	//-----------------------------------------------------------
	// HTTP Server
	//-----------------------------------------------------------
	HttpServer.init()

	//-----------------------------------------------------------
	// Datalog
	//-----------------------------------------------------------
	clearObject(datalogSettings)
	Object.assign(datalogSettings, Datalog.init(ProjectDefinitions, DatalogList, registers))
	// Restore the persisted reggisters
	Datalog.restorePersist(datalogSettings.paths.persist, registers)
	// Update Metadata
	fs.writeFileSync(datalogSettings.paths.metadata, JSON.stringify(datalogSettings.metadata, null, '\t'))
	fs.writeFileSync(datalogSettings.paths.metacsv, Datalog.metadataToCsv(datalogSettings.metadata))

	//-----------------------------------------------------------
	// Data Poster - Initialize with required configuration
	//-----------------------------------------------------------
	const dataPosterConfig = {
        // serverUrl: projectSettings.CLOUD_SERVER_URL, // might be undefined
        fine: datalogSettings.paths.fine,
        metadata: datalogSettings.paths.metadata,
		// enable : projectSettings.CLOUD_SERVER_ENABLE === undefined ? true: !!projectSettings.CLOUD_SERVER_ENABLE, 
    }
    DataPoster.init(dataPosterConfig)

	//-----------------------------------------------------------
	// System Timer
	//-----------------------------------------------------------
	// console.log('NodeRedApp.js:init()', registersNew)
	SystemTimerClear = startAlignedInterval(() => {
		const ms = Date.now()
		timestamp.ms = ms
		timestamp.s = Math.floor(ms / 1000)

		//-------------------------------------------------------
		// START of Main Polling Loop
		//-------------------------------------------------------
		Schedules.svc() // Poll the Project Schedules
		NarrativePoll(Global)  // Poll the Project Narrative
		// IoService.poll(ioServers, registers, timestamp.s) // Poll the Modbus devices

		// The IoService.poll() should be finished by now
		setTimeout(function() {
			const datalog = Datalog.svc(timestamp, datalogSettings) // Log the data

			// Check for the cloud server URL before processing data
			if (projectSettings.CLOUD_SERVER_URL && datalog) {  // Something just got written to the datalog
				DataPoster.setServerUrl(projectSettings.CLOUD_SERVER_URL);
				// pass enable to svc () 
				// Call the cloud data pusher
				// datalog is on Object with the following properties:
				// datalog.data - A Javascript array of the data to be logged
				// datalog.string - A string of the data exactly as written to the datalog file
				// datalog.coarse - A flag indicating it is a coarse log interval
				DataPoster.svc(datalog.data)

			}


			sseBroadcast({         // Broadcast Server Sent Events
				t: timestamp.s,
				count: systemTimerCounter,
			})
		}, 500)

		//-------------------------------------------------------
		// END of Main Polling Loop
		//-------------------------------------------------------

		// If a callback has been registred, call it
		systemTimerCallback?.({timestamp, count: systemTimerCounter})

		systemTimerCounter++
	}, SystemTimerInterval);

}


/**
 * Sets a callback function to be executed on each system timer tick
 * @function setSystemTimerCallback
 * @param {Function} callback - The callback function to be called on each timer tick
 * @param {Object} callback.params - The parameters passed to the callback
 * @param {Object} callback.params.timestamp - The current timestamp object
 * @param {number} callback.params.count - The current timer counter value
 * @returns {void}
 */
function setSystemTimerCallback(callback) {
	systemTimerCallback = callback
}



// process.on('SIGINT', function() {
// 	socket.close()
// 	process.exit()
// })

async function gracefulShutdown()
{
	console.log('Received kill signal, shutting down gracefully')

	systemTimerCallback = null
	SystemTimerClear?.()
	SystemTimerClear = null

	console.log('System Timer closed')


	try {
		await IoService.close(ioServers)
		console.log('IoService closed')
	} catch (err) {
		console.error('gracefulShutdown IoService.close Error', err)
	}

	// Save persisted registers
	Datalog.savePersist(datalogSettings)

	// Shutdown DataPoster
	try {
		await DataPoster.shutdown()
		console.log('DataPoster shutdown complete')
	} catch (err) {
		console.error('gracefulShutdown DataPoster.shutdown Error', err)
	}

	try {
		await HttpServer.close()
		console.log('HttpServer closed')
	} catch (err) {
		console.error('gracefulShutdown HttpServer.close Error', err)
	}


	process.exit(0)


	// return HttpServer.close().then(() => {
	// 	console.log('HttpServer closed')
	// 	return IoService.close(ioServers)
	// }).then(() => {
	// 	console.log('IoService closed')
	// 	process.exit(0)
	// }).catch(err => {
	// 	console.error('gracefulShutdown Error', err)
	// 	process.exit(1)
	// })




}

process.on('SIGTERM', gracefulShutdown)
process.on('SIGINT', gracefulShutdown)



module.exports = {
	init,
	// ioPoll,
	ioServers,
	registers,
	RegistersList,
	RegistersListClean,
	projectSettings,     // Defined in /public/project-settings.json
	timestamp,
	env: process.env,
	// systemTimerCallback,
	setSystemTimerCallback, // setSystemTimerCallback(ioServers)
}

