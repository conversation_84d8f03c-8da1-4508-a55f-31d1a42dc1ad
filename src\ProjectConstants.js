//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// ProjectConstants.js
//
// Load the project constants JSON file and initialize in Global
// Defaults for the project-constants.json file
//
//=============================================================================================
console.log('========== ProjectConstants.js =====')

const fs = require('fs')
const { getSystemMacAddress,clearObject } = require('./Util.js')
const { ProjectDefinitions } = require('./Project.js')
const { projectSettings } = require('./Global.js')
const projConstFilename = ProjectDefinitions.projectConstantsFile


const projectConstantsDefault = [
	// These are standard for all projects
	{
		name:  "PROJECT_NAME",
		value: "PROJECT_NAME",
		description: "The name that apears on the banner",
		required: true,
	},
	{
		name:  "PROJECT_LOCATION",
		value: "PROJECT_LOCATION",
		description: "Address of the project",
		required: true,
	},
	{
		name:  'ALERT_EMAIL',
		value: '<EMAIL>',
		description: "Comma separated list of email addresses to send alerts to",
		type:  'email',
		required: true,
	},
	{
		name:  'CLOUD_SERVER_URL',
		value: 'http://localhost:3000/api/app/data',
		description: "URL endpoint for posting data to external server",
		type:  'string',
		required: false,
	},{
		name:  'CLOUD_SERVER_ENABLE',
		value: true,
		description: "Enable cloud server communication",
		type:  'boolean',
		required: true,
	},
	{
		name:  'TS_MIN',
		value: -10,
		description: "Value of the temperature sensors at 4mA (°C)",
		type:  'number',
		required: true,
	},
	{
		name:  'TS_MAX',
		value: 40,
		description: "Value of the temperature sensors at 20mA (°C)",
		type:  'number',
		required: true,
	},
	{
		name:  'PS_MAX',
		value: 100,
		description: "Value of the pressure sensors at 20mA (PSIG)",
		type:  'number',
		required: true,
	},
	{
		name:  'FS_MAX',
		value: 2000,
		description: "Value of the flow sensors at 20mA (US GPM)",
		type:  'number',
		required: true,
	},
	{
		name:  'DEBUG',
		value: true,
		type:  'boolean',
		required: true,
	},
	{
		name:  'DEBUG_ALARM',
		value: false,
		type:  'boolean',
		required: true,
	},
	// These are project specific
	// "HAS_HE2": false,
	// "PURGE_TIME_DOW": -1,
	// "PURGE_TIME_HOUR": 1,
	// "PURGE_TIME_MINUTE": 0,
	// "PURGE_FLOW": 100,
	// "TEMPERATURE_TO_FLOWRATE": [
	// 	[
	// 		4, 1500
	// 	],
	// 	[
	// 		9, 450
	// 	],
	// 	[
	// 		15, 450
	// 	],
	// 	[
	// 		27, 1000
	// 	]
	// ],
	// "TARGET_SP": 0,
	// "PWHASH": "f7e4160850d29787fdad10ecaa160813",
	// "PWHASH_LOCKOUT": "f7e4160850d29787fdad10ecaa160813",
	// "BAS_CONTROL": false,
	// "MAC": ""
]

/**
 * Initializes the timestamp, projectSettings from a file,
 * and retrieving the system's MAC address.
 *
 * - Attempts to load previous project settings from a file.
 * - If the file cannot be read or parsed, applies default settings.
 * - Retrieves the system's MAC address and stores it in the project settings.
 *
 * @function init
 */
function init()
{

	// Create the project settings file if it doesn't exist
	if(!fs.existsSync(projConstFilename))
	{
		fs.writeFileSync(projConstFilename, JSON.stringify(projectConstantsDefault, null, '\t'))
		console.log('Global.init file created: ', projConstFilename)
	}

	clearObject(projectSettings)

	try {
		// Try to load previous settings
		const fileStr = fs.readFileSync(projConstFilename, 'utf8')
		const settingsArray = JSON.parse(fileStr)
		const settings = constArrayToObject(settingsArray)
		Object.assign(projectSettings, constArrayToObject(projectConstantsDefault), settings)
		console.log('Global.init projectSettings loaded')
	} catch (err) {
		console.log('Global.init Error loading ' + projConstFilename, err)
		Object.assign(projectSettings, constArrayToObject(projectConstantsDefault))
	}

	// Get the system's MAC address
	const mac = getSystemMacAddress()
	if (mac) {
		projectSettings.MAC = mac
	}

	// Get the system's Time Zone
	projectSettings.TIME_ZONE = Intl.DateTimeFormat().resolvedOptions().timeZone
}

function constArrayToObject(array)
{
	const r = {}
	array.forEach(item => {
		r[item.name] = item.value
	})
	return r
}



module.exports = {
	default: projectConstantsDefault,
	init,
}
