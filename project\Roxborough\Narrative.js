//!=============================================================================================
//! COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//! NOTICE: All information contained herein is, and remains the property of <PERSON>,
//! Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//! contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//! and are protected by trade secret or copyright law. Dissemination of this information or
//! reproduction of this material is strictly forbidden unless prior written permission is
//! obtained from Michael <PERSON> and Praevista Inc.
//!=============================================================================================
//
// Narrative.js
//
// The main narative for the `Roxborough` project.
//=============================================================================================
// In here we instanciate the core functioality of the project.

// ---- Original Narative Provided to Praevista ----
//
// System Description:
// The system consists of a 57-bore geothermal heat exchanger, two geothermal pumps (P-GEO-1 and P-GEO-2),
// a frame and plate heat exchanger connecting the geothermal loop to the building heat pump loop, and two
// building distribution pumps. The geothermal system serves as the heat source and sink for the heat pump
// system. The geothermal pumps are fully redundant (100% of flow and pressure drop) and circulate fluid
// through the geothermal heat exchanger to maintain minimum and maximum building loop supply temperatures
// for the heat pumps. An injection boiler will provide thermal energy during peak winter heating demand,
// as necessary. Refer to mechanical plans for equipment provided by the mechanical contractor.
//
// Control Sequences:
//
// Heat Exchanger Control:
//
// - Each building's respective balancing valve shall be set during test and balance to provide the design
//   flow rate and to determine the geothermal pump's maximum speed.
//
// Geothermal Pump Control:
// - Pump operation is continuous whenever the geothermal system is commanded on.
//
// - Lead/lag operation: Lead and lag pumps shall alternate every two weeks (adjustable). Changeover shall
//   occur at a pre-established time set by the system operator. The lead pump shall fully stop before the
//   lag pump starts.
//
// - If the lead pump fails, an alarm shall be generated, and the lag pump shall be commanded on.
//
// - The system minimum flow rate shall be verified during test and balance to maintain a minimum pump VFD
//   speed of 25% (adjustable). The estimated minimum flow is 180 GPM.
//
// - The lead pump shall operate to maintain both Building A and Building B loop supply temperatures within
//   the maximum and minimum temperature range specified below. Each building's supply temperature shall be
//   compared, and the building with the highest temperature difference between the current condition and
//   the mid-temperature range (50°F/10°C and 60°F/15°C) shall drive the pump speed.
//
// - If the building loop supply temperature is between 50°F (10°C) and 60°F (15°C), the lead pump shall
//   maintain minimum speed.
//
// - If the building loop supply temperature exceeds 60°F (15°C), the lead pump shall increase speed
//   linearly from minimum to maximum speed established during test and balance to achieve design flow
//   when the temperature reaches 80°F (27°C). The pump shall maintain maximum speed until the temperature
//   falls below 80°F (27°C) and ramp linearly back to minimum speed when the temperature is 60°F (15°C).
//
// - If the building loop supply temperature is below 50°F (10°C), the lead pump shall increase speed
//   linearly from minimum to maximum speed established during test and balance to achieve design flow when
//   the temperature is 40°F (4°C). The pump shall maintain maximum speed until the temperature increases to
//   40°F (4°C) and ramp linearly back to minimum speed when the temperature is 50°F (10°C).
//
// Heat Injection Control:
// - Each building's heat injection pump minimum and maximum speed shall be set during test and balance to
//   provide the design flow rate to the heat injection loop heat exchanger.
//
// - If either building's loop supply temperature drops below 35°F (1.7°C), the heat injection loop shall
//   be enabled, and the heat injection pump shall start and maintain minimum speed. The pump's minimum
//   run time shall be 30 minutes (adjustable).
//
// - The heat injection pump speed shall ramp linearly between minimum and maximum speed as the building
//   loop supply temperature ranges from 35°F (1.7°C) to 32°F (0°C), respectively.
//
// General:
// - All control sequence setpoints, timers, and thresholds shall be adjustable. Tune sequences to prevent
//   excessive equipment cycling.

// Propylene Glycol concentrations:
// - Geo loop is at 25% PG
// - Building loops are at 30% PG concentrations


// See: https://budavariam.github.io/asciiart-text/

console.log('---------- Narrative.js ------------')

// const AppImports = require('../AppImports.js')
const { Alarm } = require('../AppImports.js')


function setDefault(val, def)
{
	return val === undefined ? def : val
}

// Default settings (can be overridden by user settings)
const FLOW_SQUELCH_DEFAULT = 40 // US GPM
const DELTAT_SQUELCH_DEFAULT = 0.02 // °C
const G_MODBUS_TX_EN_DEFAULT = false
const A1_MODBUS_TX_EN_DEFAULT = false
const A2_MODBUS_TX_EN_DEFAULT = false
const DEBUG_TX_EN_DEFAULT = false

// 1 US GPM = 0.0000630902 m³/s
const GPM_TO_M3PERSEC = 0.0000630902

// const { schedules } = require('../../src/Global.js')
const {
	EnergyMeterSensorlessPumps,       // This is how we do energy metering
	ZoneControlDutyStandbyTempFlow,   // For Geo Pumping Control (two pumps)
	ZoneControlSinglePumpAuxTempFlow, // For Auxiliary Pumping Control (one pump)
} = require('./ControlFunctions.js')

const {
	PropyleneGlycol30_HeatCapacityCoeff,
	PropyleneGlycol30_DensityCoeff,
	Water_IAPWS_DensityCoeff,
	Water_IAPWS_HeatCapacityCoeff,
	BlendCoefficients,
	poly,
} = require('./FluidProperties.js')

//=======================================================================================================================
// Propylene Glycol concentrations:
//=======================================================================================================================
// - Geo loop is at 25% PG
// - Building loops are at 30% PG concentrations
//-----------------------------------------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------------------------------------------------
// Geo loop is at 25% PG: Create a custom blend of 25% PG based on 30% PG data
//-----------------------------------------------------------------------------------------------------------------------
const GeoBlendRatio        = 100 * 25 / 30 // 25% PG based on 30% PG data
const GeoDensityCoeff      = BlendCoefficients(GeoBlendRatio, PropyleneGlycol30_DensityCoeff, Water_IAPWS_DensityCoeff)
const GeoHeatCapacityCoeff = BlendCoefficients(GeoBlendRatio, PropyleneGlycol30_HeatCapacityCoeff, Water_IAPWS_HeatCapacityCoeff)
function GeoFluidDensity(T) { return poly(T, GeoDensityCoeff) }
function GeoFluidHeatCapacity(T) { return poly(T, GeoHeatCapacityCoeff) }

//-----------------------------------------------------------------------------------------------------------------------
// Building loops are at 30% PG concentrations
//-----------------------------------------------------------------------------------------------------------------------
const BuildingDensityCoeff      = PropyleneGlycol30_DensityCoeff
const BuildingHeatCapacityCoeff = PropyleneGlycol30_HeatCapacityCoeff
function BuildingFluidDensity(T) { return poly(T, BuildingDensityCoeff) }
function BuildingFluidHeatCapacity(T) { return poly(T, BuildingHeatCapacityCoeff) }


//=======================================================================================================================
// Instanciate the Thermal Energy Meters for all the zones
//=======================================================================================================================
const EnergyMeterGeothermal = new EnergyMeterSensorlessPumps()
const EnergyMeterBuilding1  = new EnergyMeterSensorlessPumps()
const EnergyMeterAuxiliary1 = new EnergyMeterSensorlessPumps()
const EnergyMeterBuilding2  = new EnergyMeterSensorlessPumps()
const EnergyMeterAuxiliary2 = new EnergyMeterSensorlessPumps()

//=======================================================================================================================
// Instanciate the Pump Controllers
//=======================================================================================================================
const ZoneControlGeothermal = new ZoneControlDutyStandbyTempFlow()
const ZoneControlAux1       = new ZoneControlSinglePumpAuxTempFlow()
const ZoneControlAux2       = new ZoneControlSinglePumpAuxTempFlow()

//=======================================================================================================================
// Alarms
//=======================================================================================================================

// const TemperatueAlarmDuration = '2h' // 2 hours
const TemperatueAlarmDuration  = '2m'
const PressureAlarmDuration    = '2m'
const CurrentLoopAlarmDuration = '2m'

const AlarmGeoLowTemperature = new Alarm({
	name:     'GeoLowTemperature',
	// setpoint: 0,      // The setpoint value (can be dynamicaly overwritten during service)
	units:    '°C',    // The units of the setpoint value
	compare:  '<',     // The comparison operator
	lockout:  false,   // If the Alarm is a lockout
	comment:  'GTS1 Geothermal Low Temperature', // A comment about the Alarm
	display:  2,       // The display decimal places
	duration: TemperatueAlarmDuration,    // The trigger duration of the event in seconds, or a time duration string
})
const AlarmGeoHighTemperature = new Alarm({
	name:     'GeoHighTemperature',
	// setpoint: 29,      // The setpoint value (can be dynamicaly overwritten during service)
	units:    '°C',    // The units of the setpoint value
	compare:  '>',     // The comparison operator
	lockout:  true,   // If the Alarm is a lockout
	comment:  'GTS1 Geothermal High Temperature', // A comment about the Alarm
	display:  2,       // The display decimal places
	duration: TemperatueAlarmDuration,    // The trigger duration of the event in seconds, or a time duration string
})

const AlarmGeoLowPressure = new Alarm({
	name:     'GeoLowPressure',
	units:    'PSIG',    // The units of the setpoint value
	compare:  '<',     // The comparison operator
	lockout:  false,   // If the Alarm is a lockout
	comment:  'GPS1 Geothermal Low Pressure', // A comment about the Alarm
	display:  2,       // The display decimal places
	duration: PressureAlarmDuration,    // The trigger duration of the event in seconds, or a time duration string
})
const AlarmGeoHighPressure = new Alarm({
	name:     'GeoHighPressure',
	units:    'PSIG',    // The units of the setpoint value
	compare:  '>',     // The comparison operator
	lockout:  false,   // If the Alarm is a lockout
	comment:  'GPS1 Geothermal High Pressure', // A comment about the Alarm
	display:  2,       // The display decimal places
	duration: PressureAlarmDuration,    // The trigger duration of the event in seconds, or a time duration string
})

//---------------------------------------------------------------
// Current Loop Alarms
//---------------------------------------------------------------
//  GPS1
const AlarmGPS1LoopError = new Alarm({
	name:     'GPS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'GPS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  GTS0
const AlarmGTS0LoopError = new Alarm({
	name:     'GTS0LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'GTS0 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  GTS1
const AlarmGTS1LoopError = new Alarm({
	name:     'GTS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'GTS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  B1TS0
const AlarmB1TS0LoopError = new Alarm({
	name:     'B1TS0LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'B1TS0 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  B1TS1
const AlarmB1TS1LoopError = new Alarm({
	name:     'B1TS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'B1TS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  A1TS0
const AlarmA1TS0LoopError = new Alarm({
	name:     'A1TS0LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'A1TS0 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  A1TS1
const AlarmA1TS1LoopError = new Alarm({
	name:     'A1TS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'A1TS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})

// Building 2
//  B2TS0
const AlarmB2TS0LoopError = new Alarm({
	name:     'B2TS0LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'B2TS0 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  B2TS1
const AlarmB2TS1LoopError = new Alarm({
	name:     'B2TS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'B2TS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  A2TS0
const AlarmA2TS0LoopError = new Alarm({
	name:     'A2TS0LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'A2TS0 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})
//  A2TS1
const AlarmA2TS1LoopError = new Alarm({
	name:     'A2TS1LoopError',
	compare:  '=',
	setpoint: true,
	comment:  'A2TS1 4-20mA Loop Error',
	duration: CurrentLoopAlarmDuration,
})

// TODO: place this functionaliity in the Alarm.js file
// Communication Alarms
const AlarmsComms = []
function initCommsAlarms(ioServers)
{
	Object.keys(ioServers).forEach(serverKey => {
		const ioServer = ioServers[serverKey]
        if(ioServer && ioServer.requestQueue && ioServer.requestQueue.length > 0)
		{
			AlarmsComms.push(new Alarm({
				// name:     ioServer.name.replaceAll('-', '_'),
				name:     ioServer.name,
				compare:  '=',
				setpoint: true,
				comment:  ioServer.name + ' (' + ioServer.host + ':' + ioServer.port + ') Comms Error',
				duration: '2m',
			}))
		}
	})
}
function AlarmsCommsService(ioServers)
{
	AlarmsComms.forEach(alarm => {
		alarm.service(ioServers[alarm.name].error.state)
	})
}


// This is called for each polling loop
function main(Global)
{
	const { registers, schedules, projectSettings, curves } = Global

	if(AlarmsComms.length === 0)
	{
		initCommsAlarms(Global.ioServers)
	}
	//-----------------------------------------------------------
	//------- Pressure Sensors -------------------------------
	//-----------------------------------------------------------
	const GPS1  = registers.AnalogIn.GPS1.value
	//-----------------------------------------------------------
	//------- Temperature Sensors -------------------------------
	//-----------------------------------------------------------
	const GTS0  = registers.AnalogIn.GTS0.value
	const GTS1  = registers.AnalogIn.GTS1.value
	const B1TS0 = registers.AnalogIn.B1TS0.value
	const B1TS1 = registers.AnalogIn.B1TS1.value
	const A1TS0 = registers.AnalogIn.A1TS0.value
	const A1TS1 = registers.AnalogIn.A1TS1.value
	const B2TS0 = registers.AnalogIn.B2TS0.value
	const B2TS1 = registers.AnalogIn.B2TS1.value
	const A2TS0 = registers.AnalogIn.A2TS0.value
	const A2TS1 = registers.AnalogIn.A2TS1.value

	const G_FLOW_SQUELCH  = (projectSettings.G_FLOW_SQUELCH || FLOW_SQUELCH_DEFAULT) * GPM_TO_M3PERSEC
	const B1_FLOW_SQUELCH = (projectSettings.B1_FLOW_SQUELCH || FLOW_SQUELCH_DEFAULT) * GPM_TO_M3PERSEC
	const A1_FLOW_SQUELCH = (projectSettings.A1_FLOW_SQUELCH || FLOW_SQUELCH_DEFAULT) * GPM_TO_M3PERSEC
	const B2_FLOW_SQUELCH = (projectSettings.B2_FLOW_SQUELCH || FLOW_SQUELCH_DEFAULT) * GPM_TO_M3PERSEC
	const A2_FLOW_SQUELCH = (projectSettings.A2_FLOW_SQUELCH || FLOW_SQUELCH_DEFAULT) * GPM_TO_M3PERSEC
	const DELTAT_SQUELCH  = (projectSettings.DELTAT_SQUELCH  || DELTAT_SQUELCH_DEFAULT)

	const G_MODBUS_TX_EN  = projectSettings.G_MODBUS_TX_EN   || G_MODBUS_TX_EN_DEFAULT
	const A1_MODBUS_TX_EN = projectSettings.A1_MODBUS_TX_EN  || A1_MODBUS_TX_EN_DEFAULT
	const A2_MODBUS_TX_EN = projectSettings.A2_MODBUS_TX_EN  || A2_MODBUS_TX_EN_DEFAULT
	const DEBUG_TX_EN     = projectSettings.DEBUG_TX_EN      || DEBUG_TX_EN_DEFAULT

	const G_PUMP_RAMP_TIME  = projectSettings.G_PUMP_RAMP_TIME  || 30
	const G_PUMP_DOWN_TIME  = projectSettings.G_PUMP_DOWN_TIME  || 60

	const A1_PUMP_RAMP_TIME = projectSettings.A1_PUMP_RAMP_TIME || 30
	const A2_PUMP_RAMP_TIME = projectSettings.A2_PUMP_RAMP_TIME || 30
	const A1_PUMP_DOWN_TIME = projectSettings.A1_PUMP_DOWN_TIME || 60
	const A2_PUMP_DOWN_TIME = projectSettings.A2_PUMP_DOWN_TIME || 60

	const A1_PUMP_RUNTIME_SEC = (projectSettings.A1_PUMP_RUNTIME_MIN || 30) * 60
	const A2_PUMP_RUNTIME_SEC = (projectSettings.A2_PUMP_RUNTIME_MIN || 30) * 60

	//#########################################################################################
	//   _____  _   _  _____  ____    ____ __   __  __  __  _____  _____  _____  ____
	//  | ____|| \ | || ____||  _ \  / ___|\ \ / / |  \/  || ____||_   _|| ____||  _ \
	//  |  _|  |  \| ||  _|  | |_) || |  _  \ V /  | |\/| ||  _|    | |  |  _|  | |_) |
	//  | |___ | |\  || |___ |  _ < | |_| |  | |   | |  | || |___   | |  | |___ |  _ <
	//  |_____||_| \_||_____||_| \_\ \____|  |_|   |_|  |_||_____|  |_|  |_____||_| \_\
	//
	// Energy Metering
	//#########################################################################################

	//=========================================================================================
	//    ____               _    _                                    _
	//   / ___|  ___   ___  | |_ | |__    ___  _ __  _ __ ___    __ _ | |
	//  | |  _  / _ \ / _ \ | __|| '_ \  / _ \| '__|| '_ ` _ \  / _` || |
	//  | |_| ||  __/| (_) || |_ | | | ||  __/| |   | | | | | || (_| || |
	//   \____| \___| \___/  \__||_| |_| \___||_|   |_| |_| |_| \__,_||_|
	//
	// G  --- Energy Metering for the Geothermal Ground HX
	//=========================================================================================
	EnergyMeterGeothermal.calculate({
		// Read-only
		timestamp: Global.timestamp.s,
		T0:    GTS0,   // Ground Loop Supply Temperature
		T1:    GTS1,   // Ground Loop Return Temperature
		Tflow: GTS0,   // Temperature of the fluid where flow is measured (for density calculation)
		PumpFlows: [   // Flow values of the pumps (can be any number of pumps in tandem)
			registers.GP1.SensorlessFlow.value * GPM_TO_M3PERSEC,
			registers.GP2.SensorlessFlow.value * GPM_TO_M3PERSEC,
		],
		DeltaTSquelch: DELTAT_SQUELCH,
		FlowSquelch: G_FLOW_SQUELCH,
		SensorError: registers.AnalogIn.GTS0.error || registers.AnalogIn.GTS1.error,

		// Writable Registers
		DeltaT:       registers.geothermal.DeltaT, // ΔT across ground loop
		Density:      registers.geothermal.FluidDensity,
		HeatCapacity: registers.geothermal.FluidHeatCapacity,
		Mode:         registers.geothermal.HeatingCoolingMode,   // sets to 'off', 'heat', or 'cool'

		// Energy Flow (Writable)
		HeatTransferRate: registers.geothermal.HeatTransferRate,

		// Cumulative Energy (Writable)
		HeatTransferIn:   registers.geothermal.HeatTransferIn,
		HeatTransferOut:  registers.geothermal.HeatTransferOut,
		DensityFunction:  GeoFluidDensity,
		HeatCapacityFunction: GeoFluidHeatCapacity,

		// Temperature values for the heat transfer calculations
		DensityTemperature: registers.geothermal.FluidDensityT,
		HeatCapacityTemperature: registers.geothermal.FluidHeatCapacityT,

	})

	//=========================================================================================
	//   ____          _  _      _  _                       _
	//  | __ )  _   _ (_)| |  __| |(_) _ __    __ _        / |
	//  |  _ \ | | | || || | / _` || || '_ \  / _` | _____ | |
	//  | |_) || |_| || || || (_| || || | | || (_| ||_____|| |
	//  |____/  \__,_||_||_| \__,_||_||_| |_| \__, |       |_|
	//                                        |___/
	// B1 --- Energy Metering for the Building 1 Loop
	//=========================================================================================
	EnergyMeterBuilding1.calculate({
		// Read-only
		timestamp: Global.timestamp.s,
		T0:    B1TS0,   // Ground Loop Supply Temperature
		T1:    B1TS1,   // Ground Loop Return Temperature
		Tflow: B1TS1,   // Temperature of the fluid where flow is measured (for density calculation)
		PumpFlows: [   // Flow values of the pumps (can be any number of pumps in tandem)
			registers.B1P1.SensorlessFlow.value * GPM_TO_M3PERSEC,
			registers.B1P2.SensorlessFlow.value * GPM_TO_M3PERSEC,
		],
		FlowSquelch: B1_FLOW_SQUELCH,
		SensorError: registers.AnalogIn.B1TS0.error || registers.AnalogIn.B1TS1.error,

		// Writable Registers
		DeltaT:       registers.building1.DeltaT, // ΔT across ground loop
		Density:      registers.building1.FluidDensity,
		HeatCapacity: registers.building1.FluidHeatCapacity,
		Mode:         registers.building1.HeatingCoolingMode,   // sets to 'off', 'heat', or 'cool'

		// Energy Flow (Writable)
		HeatTransferRate: registers.building1.HeatTransferRate,

		// Cumulative Energy (Writable)
		HeatTransferIn:   registers.building1.HeatTransferIn,
		HeatTransferOut:  registers.building1.HeatTransferOut,

		// Functions for Density and Heat Capacity as a function of temperature
		DensityFunction:  BuildingFluidDensity,
		HeatCapacityFunction: BuildingFluidHeatCapacity,

		// Temperature values for the heat transfer calculations
		DensityTemperature: registers.building1.FluidDensityT,
		HeatCapacityTemperature: registers.building1.FluidHeatCapacityT,
	})

	//=========================================================================================
	//      _                 _  _  _                             _
	//     / \   _   _ __  __(_)| |(_)  __ _  _ __  _   _        / |
	//    / _ \ | | | |\ \/ /| || || | / _` || '__|| | | | _____ | |
	//   / ___ \| |_| | >  < | || || || (_| || |   | |_| ||_____|| |
	//  /_/   \_\\__,_|/_/\_\|_||_||_| \__,_||_|    \__, |       |_|
	//                                              |___/
	// A1 --- Energy Metering for the Auxiliary 1 Heating Loop
	//=========================================================================================
	EnergyMeterAuxiliary1.calculate({
		// Read-only
		timestamp: Global.timestamp.s,
		T0:    A1TS0,   // The Temperature closest to the ground supply
		T1:    A1TS1,   // The Temperature farthest from the ground supply
		Tflow: A1TS0,   // Temperature of the fluid where flow is measured (for density calculation)
		PumpFlows: [   // Flow values of the pumps (can be any number of pumps in tandem)
			registers.A1P1.SensorlessFlow.value * GPM_TO_M3PERSEC,
		],
		FlowSquelch: A1_FLOW_SQUELCH,
		SensorError: registers.AnalogIn.A1TS0.error || registers.AnalogIn.A1TS1.error,

		// Writable Registers
		DeltaT:       registers.auxiliary1.DeltaT, // ΔT across ground loop
		Density:      registers.auxiliary1.FluidDensity,
		HeatCapacity: registers.auxiliary1.FluidHeatCapacity,
		Mode:         registers.auxiliary1.HeatingCoolingMode,   // sets to 'off', 'heat', or 'cool'

		// Energy Flow (Writable)
		HeatTransferRate: registers.auxiliary1.HeatTransferRate,

		// Cumulative Energy (Writable)
		HeatTransferIn:   registers.auxiliary1.HeatTransferIn,
		HeatTransferOut:  registers.auxiliary1.HeatTransferOut,

		// Functions for Density and Heat Capacity as a function of temperature
		DensityFunction:  BuildingFluidDensity,
		HeatCapacityFunction: BuildingFluidHeatCapacity,

		// Temperature values for the heat transfer calculations
		DensityTemperature: registers.auxiliary1.FluidDensityT,
		HeatCapacityTemperature: registers.auxiliary1.FluidHeatCapacityT,
	})


	//=========================================================================================
	//   ____          _  _      _  _                      ____
	//  | __ )  _   _ (_)| |  __| |(_) _ __    __ _       |___ \
	//  |  _ \ | | | || || | / _` || || '_ \  / _` | _____  __) |
	//  | |_) || |_| || || || (_| || || | | || (_| ||_____|/ __/
	//  |____/  \__,_||_||_| \__,_||_||_| |_| \__, |      |_____|
	//                                        |___/
	// B2 --- Energy Metering for the Building 2 Loop
	//=========================================================================================
	EnergyMeterBuilding2.calculate({
		// Read-only
		timestamp: Global.timestamp.s,
		T0:    B2TS0,   // Ground Loop Supply Temperature
		T1:    B2TS1,   // Ground Loop Return Temperature
		Tflow: B2TS1,   // Temperature of the fluid where flow is measured (for density calculation)
		PumpFlows: [   // Flow values of the pumps (can be any number of pumps in tandem)
			registers.B2P1.SensorlessFlow.value * GPM_TO_M3PERSEC,
			registers.B2P2.SensorlessFlow.value * GPM_TO_M3PERSEC,
		],
		FlowSquelch: B2_FLOW_SQUELCH,
		SensorError: registers.AnalogIn.B2TS0.error || registers.AnalogIn.B2TS1.error,

		// Writable Registers
		DeltaT:       registers.building2.DeltaT, // ΔT across ground loop
		Density:      registers.building2.FluidDensity,
		HeatCapacity: registers.building2.FluidHeatCapacity,
		Mode:         registers.building2.HeatingCoolingMode,   // sets to 'off', 'heat', or 'cool'

		// Energy Flow (Writable)
		HeatTransferRate: registers.building2.HeatTransferRate,

		// Cumulative Energy (Writable)
		HeatTransferIn:   registers.building2.HeatTransferIn,
		HeatTransferOut:  registers.building2.HeatTransferOut,

		// Functions for Density and Heat Capacity as a function of temperature
		DensityFunction:  BuildingFluidDensity,
		HeatCapacityFunction: BuildingFluidHeatCapacity,

		// Temperature values for the heat transfer calculations
		DensityTemperature: registers.building2.FluidDensityT,
		HeatCapacityTemperature: registers.building2.FluidHeatCapacityT,
	})


	//=========================================================================================
	//      _                 _  _  _                            ____
	//     / \   _   _ __  __(_)| |(_)  __ _  _ __  _   _       |___ \
	//    / _ \ | | | |\ \/ /| || || | / _` || '__|| | | | _____  __) |
	//   / ___ \| |_| | >  < | || || || (_| || |   | |_| ||_____|/ __/
	//  /_/   \_\\__,_|/_/\_\|_||_||_| \__,_||_|    \__, |      |_____|
	//                                              |___/
	// A2 --- Energy Metering for the Auxiliary 2 Heating Loop
	//=========================================================================================
	EnergyMeterAuxiliary2.calculate({
		// Read-only
		timestamp: Global.timestamp.s,
		T0:    A2TS0,   // The Temperature closest to the ground supply
		T1:    A2TS1,   // The Temperature farthest from the ground supply
		Tflow: A2TS0,   // Temperature of the fluid where flow is measured (for density calculation)
		PumpFlows: [   // Flow values of the pumps (can be any number of pumps in tandem)
			registers.A2P1.SensorlessFlow.value * GPM_TO_M3PERSEC,
		],
		FlowSquelch: A2_FLOW_SQUELCH,
		SensorError: registers.AnalogIn.A2TS0.error || registers.AnalogIn.A2TS1.error,

		// Writable Registers
		DeltaT:       registers.auxiliary2.DeltaT, // ΔT across ground loop
		Density:      registers.auxiliary2.FluidDensity,
		HeatCapacity: registers.auxiliary2.FluidHeatCapacity,
		Mode:         registers.auxiliary2.HeatingCoolingMode,   // sets to 'off', 'heat', or 'cool'

		// Energy Flow (Writable)
		HeatTransferRate: registers.auxiliary2.HeatTransferRate,

		// Cumulative Energy (Writable)
		HeatTransferIn:   registers.auxiliary2.HeatTransferIn,
		HeatTransferOut:  registers.auxiliary2.HeatTransferOut,

		// Functions for Density and Heat Capacity as a function of temperature
		DensityFunction:  BuildingFluidDensity,
		HeatCapacityFunction: BuildingFluidHeatCapacity,

		// Temperature values for the heat transfer calculations
		DensityTemperature: registers.auxiliary2.FluidDensityT,
		HeatCapacityTemperature: registers.auxiliary2.FluidHeatCapacityT,
	})


	//#########################################################################################
	//   ____                              ____               _                _
	//  |  _ \  _   _  _ __ ___   _ __    / ___| ___   _ __  | |_  _ __  ___  | |
	//  | |_) || | | || '_ ` _ \ | '_ \  | |    / _ \ | '_ \ | __|| '__|/ _ \ | |
	//  |  __/ | |_| || | | | | || |_) | | |___| (_) || | | || |_ | |  | (_) || |
	//  |_|     \__,_||_| |_| |_|| .__/   \____|\___/ |_| |_| \__||_|   \___/ |_|
	//                           |_|
	//#########################################################################################

	//=========================================================================================
	//    ____               _    _                                    _
	//   / ___|  ___   ___  | |_ | |__    ___  _ __  _ __ ___    __ _ | |
	//  | |  _  / _ \ / _ \ | __|| '_ \  / _ \| '__|| '_ ` _ \  / _` || |
	//  | |_| ||  __/| (_) || |_ | | | ||  __/| |   | | | | | || (_| || |
	//   \____| \___| \___/  \__||_| |_| \___||_|   |_| |_| |_| \__,_||_|
	//
	// G  --- Pump Control for the Geothermal Ground HX
	//=========================================================================================
	ZoneControlGeothermal.calculate({
		// Read Only Constants
		PumpRampTime: G_PUMP_RAMP_TIME, // Pumps should turn on in this amount of seconds
		PumpDownTime: G_PUMP_DOWN_TIME, // Pumps should shut down in this amount of seconds

		// Read Only Registers
		PumpCurve: curves.GeoCurve,
		PurgeSchedule: schedules.GeoPurgeSchedule.value, // Purge Mode
		// ControlSetpoint will be the worst case bulding entering temperature
		ControlSensor: [B1TS1, B2TS1],

		// Decision about which pump to use is based in this information
		PumpRunningHours: [
			registers.GP1.TripPumpRunningHours.value,
			registers.GP2.TripPumpRunningHours.value
		],
		PumpHOAState: [
			registers.GP1.HOAState.value,
			registers.GP2.HOAState.value
		],
		PumpStatus: [
			registers.GP1.Status.value,
			registers.GP2.Status.value
		],
		// Pump is active if TripPumpRunningHours has been written
		PumpOnline: [
			registers.GP1.TripPumpRunningHours.updated > 0,
			registers.GP2.TripPumpRunningHours.updated > 0
		],
		PumpingEnabled:  setDefault(projectSettings.G_PUMPING_ENABLE, true),
		// PumpingEnabled: registers.geothermal.PumpingEnabled.value,

		// Write Registers
		ControlSetpoint: registers.geothermal.ControlSetpoint,
		PumpingState:    registers.geothermal.PumpingState,
		PumpHOASet: [
			registers.geothermal.HOAState1,
			registers.geothermal.HOAState2
		],
		SwitchoverEvent: schedules.GeoPumpSwitchover,
		// PDownFault: [
		// 	registers.geothermal.PDownFault1,
		// 	registers.geothermal.PDownFault2
		// ]


	})

	// Write the ControlSetpoint to the pumps
	if(G_MODBUS_TX_EN)
	{
		registers.GP1.ControlSetpointWrite.write(registers.geothermal.ControlSetpoint.value)
		registers.GP2.ControlSetpointWrite.write(registers.geothermal.ControlSetpoint.value)

		registers.GP1.HOAStateWrite.write(registers.geothermal.HOAState1.value)
		registers.GP2.HOAStateWrite.write(registers.geothermal.HOAState2.value)
	}


	//=========================================================================================
	//      _                 _  _  _                             _
	//     / \   _   _ __  __(_)| |(_)  __ _  _ __  _   _        / |
	//    / _ \ | | | |\ \/ /| || || | / _` || '__|| | | | _____ | |
	//   / ___ \| |_| | >  < | || || || (_| || |   | |_| ||_____|| |
	//  /_/   \_\\__,_|/_/\_\|_||_||_| \__,_||_|    \__, |       |_|
	//                                              |___/
	// A1 --- Pump Control for the Auxiliary 1 Heating Loop
	//=========================================================================================
	ZoneControlAux1.calculate({
		// Read Only Constants
		PumpRampTime: A1_PUMP_RAMP_TIME, // ramp time in seconds
		PumpDownTime: A1_PUMP_DOWN_TIME, // time in seconds to wait for pump to ramp down
		PumpMinRuntime: A1_PUMP_RUNTIME_SEC,  // minimum runtime in seconds

		PumpOnCondition:  B1TS1 <= curves.AuxCurve1[1][0], // This curve has only 2 points, using the x (temperature) value of the second point
		PumpOffCondition: B1TS1 >  curves.AuxCurve1[1][0] + 0.1,  // A little hysteresis

		// Pump Curve
		PumpCurve: curves.AuxCurve1, // Pump control curve

		// RO Temperature sensor
		ControlSensor: B1TS1, // This triggers the pump and sets the curve

		// RO Pump Registers
		PumpHOAState: registers.A1P1.HOAState.value,
		PumpStatus:   registers.A1P1.Status.value,
		PumpOnline:   registers.A1P1.TripPumpRunningHours.updated > 0,

		PumpingEnabled:  setDefault(projectSettings.A1_PUMPING_ENABLE, true),
		// PumpingEnabled:  registers.auxiliary1.PumpingEnabled.value,

		// Write Registers
		ControlSetpoint: registers.auxiliary1.ControlSetpoint,
		PumpHOASet:      registers.auxiliary1.HOAState,
		PumpingState:    registers.auxiliary1.PumpingState,
		PumpRuntime:     registers.auxiliary1.PumpRuntime,
	})

	if(A1_MODBUS_TX_EN)
	{
		registers.A1P1.ControlSetpointWrite.write(registers.auxiliary1.ControlSetpoint.value)
		registers.A1P1.HOAStateWrite.write(registers.auxiliary1.HOAState.value)
	}


	//=========================================================================================
	//      _                 _  _  _                            ____
	//     / \   _   _ __  __(_)| |(_)  __ _  _ __  _   _       |___ \
	//    / _ \ | | | |\ \/ /| || || | / _` || '__|| | | | _____  __) |
	//   / ___ \| |_| | >  < | || || || (_| || |   | |_| ||_____|/ __/
	//  /_/   \_\\__,_|/_/\_\|_||_||_| \__,_||_|    \__, |      |_____|
	//                                              |___/
	// A2 --- Pump Control for the Auxiliary 2 Heating Loop
	//=========================================================================================
	ZoneControlAux2.calculate({
		// Read Only Constants
		PumpRampTime: A2_PUMP_RAMP_TIME, // ramp time in seconds
		PumpDownTime: A2_PUMP_DOWN_TIME, // time in seconds to wait for pump to ramp down
		PumpMinRuntime: A2_PUMP_RUNTIME_SEC,  // minimum runtime in seconds

		PumpOnCondition:  B2TS1 <= curves.AuxCurve2[1][0], // This curve has only 2 points, using the x (temperature) value of the second point
		PumpOffCondition: B2TS1 >  curves.AuxCurve2[1][0] + 0.1,  // A little hysteresis

		// Pump Curve
		PumpCurve: curves.AuxCurve2, // Pump control curve

		// RO Temperature sensor
		ControlSensor: B2TS1, // This triggers the pump and sets the curve

		// RO Pump Registers
		PumpHOAState: registers.A2P1.HOAState.value,
		PumpStatus:   registers.A2P1.Status.value,
		PumpOnline:   registers.A2P1.TripPumpRunningHours.updated > 0,

		PumpingEnabled:  setDefault(projectSettings.A2_PUMPING_ENABLE, true),
		// PumpingEnabled:  registers.auxiliary2.PumpingEnabled.value,

		// Write Registers
		ControlSetpoint: registers.auxiliary2.ControlSetpoint,
		PumpHOASet:      registers.auxiliary2.HOAState,
		PumpingState:    registers.auxiliary2.PumpingState,
		PumpRuntime:     registers.auxiliary2.PumpRuntime,
	})

	if(A2_MODBUS_TX_EN)
	{
		registers.A2P1.ControlSetpointWrite.write(registers.auxiliary2.ControlSetpoint.value)
		registers.A2P1.HOAStateWrite.write(registers.auxiliary2.HOAState.value)
	}




	// Temperature and Pressure Alarms
	AlarmGeoLowTemperature.service (registers.AnalogIn.GTS1, { setpoint: projectSettings.ALARM_GEO_LOW_TEMP  || 0 })
	AlarmGeoHighTemperature.service(registers.AnalogIn.GTS1, { setpoint: projectSettings.ALARM_GEO_HIGH_TEMP || 29 })
	AlarmGeoLowPressure.service    (registers.AnalogIn.GPS1, { setpoint: projectSettings.ALARM_GEO_LOW_PRESSURE  || 4 })
	AlarmGeoHighPressure.service   (registers.AnalogIn.GPS1, { setpoint: projectSettings.ALARM_GEO_HIGH_PRESSURE || 40 })

	// Current Loop Error
	AlarmGPS1LoopError.service (registers.AnalogIn.GPS1.error)
	AlarmGTS0LoopError.service (registers.AnalogIn.GTS0.error)
	AlarmGTS1LoopError.service (registers.AnalogIn.GTS1.error)
	AlarmB1TS0LoopError.service(registers.AnalogIn.B1TS0.error)
	AlarmB1TS1LoopError.service(registers.AnalogIn.B1TS1.error)
	AlarmA1TS0LoopError.service(registers.AnalogIn.A1TS0.error)
	AlarmA1TS1LoopError.service(registers.AnalogIn.A1TS1.error)
	AlarmB2TS0LoopError.service(registers.AnalogIn.B2TS0.error)
	AlarmB2TS1LoopError.service(registers.AnalogIn.B2TS1.error)
	AlarmA2TS0LoopError.service(registers.AnalogIn.A2TS0.error)
	AlarmA2TS1LoopError.service(registers.AnalogIn.A2TS1.error)

	// Comms alarms
	AlarmsCommsService(Global.ioServers)








	//-----------------------------------------------------------
	// Test the Digital Outputs
	// Implements a 1-hot activation of the digital outputs
	//-----------------------------------------------------------
	if(DEBUG_TX_EN)
	{
		const DigitalOutRead = (registers.DigitalIO.DigitalOutRead.value * 2) & 0xFF
		// registers.DigitalIO.DigitalOutWrite.value = DigitalOutRead ? DigitalOutRead : 1
		registers.DigitalIO.DigitalOutWrite.write(DigitalOutRead ? DigitalOutRead : 1)
	}

}

module.exports = main
