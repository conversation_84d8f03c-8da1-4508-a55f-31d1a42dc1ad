// /public/src/Logger.js
const winston = require('winston');
const path = require('path');
const fs = require('fs');
const MemoryTransport = require('./transports/MemoryTransports'); // Path to your custom RAM transport

// --- Configuration Constants ---
// Resolve path for error logs: go up two levels from /public/src to project root, then into 'logs'
const ERROR_LOG_DIR = process.env.LOG_DIR || path.resolve(__dirname, '../../logs');
const RAM_LOG_MAX_SIZE = 5 * 1024 * 1024; // 5MB for RAM-based logs (info, debug, warn, etc.)
const ERROR_FILE_MAX_SIZE = 1 * 1024 * 1024; // 1MB for the error log file
const ERROR_FILE_MAX_FILES = 1; // <PERSON>'s default will keep current.log and current.log.1

// --- Ensure Log Directory Exists ---
// This is critical for file transports to function correctly.
try {
    if (!fs.existsSync(ERROR_LOG_DIR)) {
        fs.mkdirSync(ERROR_LOG_DIR, { recursive: true });
        console.log(`Logger: Created error log directory at: ${ERROR_LOG_DIR}`);
    }
} catch (e) {
    console.error(`Logger: CRITICAL - Failed to create log directory ${ERROR_LOG_DIR}:`, e.message);
    // If directory creation fails, file logging might not work. App should proceed but be aware.
}

// --- Define Custom Log Levels ---
// Winston's default levels are: error, warn, info, http, verbose, debug, silly (from lowest to highest severity)
// You can customize them if needed, but defaults are usually fine.
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    verbose: 4,
    debug: 5,
    silly: 6
};

// --- Custom Error Log Format and Filtering ---
const errorLogFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // Add timestamp
    // Use printf for custom formatting
    winston.format.printf(info => {
        // Start with basic log message
        let logMessage = `[${info.timestamp}] [${info.level.toUpperCase()}]: ${info.message}`;

        // Prepare a container for additional metadata
        const filteredMeta = {};

        // Iterate over all properties in the log `info` object
        for (const key in info) {
            // Skip Winston's internal properties, and properties we've explicitly handled (level, message, timestamp, stack)
            if (['level', 'message', 'timestamp', 'splat', 'stack', Symbol.for('level'), Symbol.for('message')].includes(key)) {
                continue;
            }

            const value = info[key];

            // Filter out functions and symbols to keep logs clean
            if (typeof value === 'function' || typeof value === 'symbol') {
                continue;
            }

            // Handle objects and arrays: summarize them to avoid excessively large logs
            if (typeof value === 'object' && value !== null) {
                if (value instanceof Error) {
                    // If it's an Error object, its message and stack are handled directly or via `info.stack`
                    continue; // Skip the error object itself in meta
                } else if (Array.isArray(value)) {
                    // Summarize arrays if they are large
                    if (value.length > 5 && JSON.stringify(value).length > 200) { // If array has many items or large content
                        filteredMeta[key] = `[Array len:${value.length}]`;
                    } else {
                        filteredMeta[key] = value; // Include smaller arrays directly
                    }
                } else {
                    // Handle general objects: stringify, but limit size and catch circular references
                    try {
                        const objString = JSON.stringify(value);
                        if (objString.length > 500) { // Limit length of stringified object in log
                            filteredMeta[key] = `[Object too large, size: ${objString.length} chars]`;
                        } else {
                            filteredMeta[key] = value; // Include smaller objects directly
                        }
                    } catch (e) {
                        filteredMeta[key] = '[Circular/Unstringifyable Object]'; // Indicate unparseable objects
                    }
                }
            } else {
                // Include primitive types directly (strings, numbers, booleans, null, undefined)
                filteredMeta[key] = value;
            }
        }

        // Append filtered metadata if any exists
        const metaKeys = Object.keys(filteredMeta);
        if (metaKeys.length > 0) {
            logMessage += ` ${JSON.stringify(filteredMeta)}`;
        }

        // Add stack trace specifically for error level messages
        // Ensure it's not duplicated if already part of info.message (though Winston usually separates)
        if (info.level === 'error' && info.stack) {
             // Add a newline and indent for readability of the stack trace
             logMessage += `\nStack:\n${info.stack}`;
        }

        return logMessage;
    })
);

// --- Winston Logger Instance Configuration ---
const logger = winston.createLogger({
    levels: levels, // Apply custom levels
    // Default format for logs *before* transports format them (important for passing `stack` and `meta`)
    format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // Timestamp for internal use and formatters
        winston.format.errors({ stack: true }), // Crucial: Ensures `info.stack` property is populated for Error objects
        winston.format.json() // A general JSON format useful for passing all data to transports
                              // Transports then pick and choose what to display/store.
    ),
    transports: [
        // 1. RAM-based transport for volatile logs (info, debug, warn, etc.)
        new MemoryTransport({
            level: 'info', // Logs levels info, warn, error, http, verbose, debug, silly to RAM
            maxSize: RAM_LOG_MAX_SIZE
        }),

        // 2. File transport for all errors (caught exceptions, uncaught exceptions, unhandled rejections)
        new winston.transports.File({
            filename: path.join(ERROR_LOG_DIR, 'application_errors.log'),
            level: 'error', // ONLY error level messages go to this file
            format: errorLogFormat, // Apply the custom error format
            maxsize: ERROR_FILE_MAX_SIZE, // 1MB file size limit
            maxFiles: ERROR_FILE_MAX_FILES, // When current file fills, it's renamed, and a new one starts.
                                            // So you'll typically have 'application_errors.log' (current)
                                            // and 'application_errors.log.1' (the previous one).
            tailable: true, // Appends to the end of the file.
            // These properties tell Winston to automatically capture global Node.js errors:
            handleExceptions: true,   // Catches uncaught exceptions (e.g., synchronous errors not in try/catch)
            handleRejections: true    // Catches unhandled promise rejections
        }),

        // 3. Console transport for real-time visibility during development/debugging
        new winston.transports.Console({
            // Set console log level based on environment
            level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
            // Custom format for console output (readable, colorized)
            format: winston.format.combine(
                winston.format.colorize(), // Add colors to console output
                winston.format.simple(), // Simple format like "level: message"
                winston.format.printf(info => {
                    // Include timestamp, level, message, and stack for errors for console readability
                    let msg = `[${info.timestamp.substring(11, 19)}] [${info.level}] ${info.message}`;
                    if (info.stack) {
                        msg += `\n${info.stack}`; // Add stack trace for errors
                    }
                    return msg;
                })
            )
        })
    ],
    // Do not exit the process on handled exceptions; PM2 will manage restarts if truly crashed.
    exitOnError: false
});

// --- Export the Configured Logger Instance ---
module.exports = logger;